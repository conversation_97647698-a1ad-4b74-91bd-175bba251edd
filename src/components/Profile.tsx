import React, { useState, useMemo } from 'react';
import { Modal, Typography, Space, Divider } from 'antd';
import { ArrowLeftOutlined, EditFilled } from '@ant-design/icons';
import ImageUploadWithCrop from './ImageUploadWithCrop';
import { useLanguage } from '@/hooks/useLanguage';
import { getDisplayAddress } from '@/utils/utils';
import { isArtist } from '@/utils/roleUtils';
import type { UserProfileResponse } from '@/types/api';
import { Button } from 'antd';
import { useAuthStore } from '@/store/authStore';
import FormButton from '@/pages/Register/coms/FormButton';

interface ProfileProps {
  visible: boolean;
  onClose: () => void;
}

const Profile: React.FC<ProfileProps> = ({ visible, onClose }) => {
  const { t } = useLanguage();
  const { isArtist, isInvestor } = useAuthStore.getState();
  // 模拟用户信息
  const userInfo: UserProfileResponse = {
    email: 'zhang<PERSON>@example.com',
    alias: 'zhang san',
    firstName: '三',
    lastName: '张',
    addressLine1:
      '桥西区春风路100号号号号号号号号号号号号号号号号号号号号号号号号号号号号号号号号号号号号号号号',
    addressLine2: '石家庄市',
    stateProvince: '河北省',
    countryCode: 'CN',
    postalZipCode: '100000',
    avatarUrl:
      'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
    stageName: '',
    bio: '',
    displayName: '张三',
    accountId: '**********',
    mobile: '**********',
  };
  const displayAddress = useMemo(() => {
    return getDisplayAddress(userInfo);
  }, [userInfo]);

  // 头像上传成功处理
  const handleAvatarUploadSuccess = (url: string) => {
    // 这里可以调用API保存头像URL到后端
    console.log('头像上传成功:', url);
  };

  // 自定义上传请求（这里可以替换为实际的上传API）
  const customAvatarUpload = (options: any) => {
    const { file, onSuccess, onError } = options;

    // 模拟上传过程
    setTimeout(() => {
      if (Math.random() > 0.1) {
        // 90% 成功率
        const mockUrl = URL.createObjectURL(file);
        onSuccess({ url: mockUrl });
      } else {
        onError(new Error('上传失败'));
      }
    }, 1000);
  };
  interface ProfileItem {
    label: string;
    value: string;
  }
  const generateProfileItem = (item: ProfileItem) => {
    return (
      <div className="flex items-start ">
        <span className="text-label  mr-6px w-100px text-right -ml-100px">
          {item.label}:
        </span>
        <div className="flex-1">
          <span className="text-white">{item.value}</span>
          <EditFilled className="ml-10px cursor-pointer" />
        </div>
      </div>
    );
  };
  interface EditProfileItem extends ProfileItem {
    name: ProfileName;
  }
  const aliasEditElement = (item: EditProfileItem) => {
    return (
      <div className="flex items-start ">
        <span className="text-label  mr-6px w-100px text-right -ml-100px">
          {item.label}:
        </span>
        <div className="flex-1">
          <span className="text-white">{item.value}</span>
          <EditFilled className="ml-10px cursor-pointer" />
        </div>
      </div>
    );
  };
  /**
   * 使用 TypeScript 的 as const 和 typeof 语法，将数组直接转换为枚举类型
   */
  const profileNameList = [
    'alias',
    'name',
    'email',
    'address',
    'stageName',
    'bio',
  ] as const;
  type ProfileName = (typeof profileNameList)[number];

  interface ProfileListItem {
    name: ProfileName;
    editable: boolean;
    _id?: number;
    showElement: React.ReactNode;
    editElement?: React.ReactNode;
  }
  const renderProfileInfo = () => {
    let profileList: ProfileListItem[] = [
      {
        name: 'alias',
        editable: false,
        showElement: generateProfileItem({
          label: t('common.alias'),
          value: userInfo.alias,
        }),
        editElement: aliasEditElement({
          name: 'alias',
          label: t('common.alias'),
          value: userInfo.alias,
        }),
      },
      {
        name: 'name',
        editable: false,
        showElement: generateProfileItem({
          label: t('common.name'),
          value: userInfo.displayName,
        }),
        editElement: aliasEditElement({
          name: 'name',
          label: t('common.name'),
          value: userInfo.displayName,
        }),
      },
      {
        name: 'email',
        editable: false,
        showElement: generateProfileItem({
          label: t('common.email'),
          value: userInfo.email,
        }),
        editElement: aliasEditElement({
          name: 'email',
          label: t('common.email'),
          value: userInfo.email,
        }),
      },
      {
        name: 'address',
        editable: false,
        showElement: generateProfileItem({
          label: t('common.address'),
          value: displayAddress,
        }),
        editElement: aliasEditElement({
          name: 'address',
          label: t('common.address'),
          value: displayAddress,
        }),
      },
    ];
    const artistProfileList: ProfileListItem[] = [
      {
        name: 'stageName',
        editable: false,
        showElement: generateProfileItem({
          label: t('common.stageName'),
          value: userInfo.stageName,
        }),
        editElement: aliasEditElement({
          name: 'stageName',
          label: t('common.stageName'),
          value: userInfo.stageName,
        }),
      },
      {
        name: 'bio',
        editable: false,
        showElement: generateProfileItem({
          label: t('common.artistBio'),
          value: userInfo.bio,
        }),
        editElement: aliasEditElement({
          name: 'bio',
          label: t('common.artistBio'),
          value: userInfo.bio,
        }),
      },
    ];

    console.log('isArtist----', isArtist);

    // if (isArtist) {
    profileList = profileList.concat(artistProfileList);
    // }
    profileList.forEach((item, index) => {
      item._id = index;
    });

    return (
      <div className=" flex flex-col gap-15px">
        {profileList.map(item => {
          return item.editable ? item.editElement : item.showElement;
        })}
      </div>
    );
  };

  if (!visible) return null;
  return (
    <Modal
      open={visible}
      onCancel={onClose}
      width={1000}
      className="
      [&.ant-modal_.ant-modal-close]:(inset-ie-auto ml-12px top-40px)
      [&.ant-modal_.ant-modal-content]:( px-50px pt-35px pb-112px) "
      footer={null}
      keyboard={false}
      maskClosable={false}
      centered
      closeIcon={
        <div className="flex items-center gap-2 hover:opacity-45">
          <ArrowLeftOutlined
            style={{ fontSize: '16px', color: 'var(--color-label)' }}
          />
          <span className="text-#B5B5B5 font-400">Back</span>
        </div>
      }
      title={
        <div className=" text-center text-white text-32px font-700">
          Profile
        </div>
      }
    >
      <div className="flex flex-col items-center pt-55px w-500px mx-auto ">
        {/* 头像区域 */}
        <div className="flex flex-col items-center mb-30px">
          <ImageUploadWithCrop
            avatarUrl={userInfo.avatarUrl}
            avatarSize={230}
            userName={userInfo.alias}
            onUploadSuccess={handleAvatarUploadSuccess}
            customRequest={customAvatarUpload}
          />
        </div>

        {/* 用户信息区域 */}
        <div className="w-full text-12px">
          <div className="text-center text-white text-22px font-medium mb-30px">
            {userInfo.displayName}
          </div>
          <div className="mb-80px">{renderProfileInfo()}</div>
          <div>
            
          </div>
          <div className="flex justify-center gap-20px">
            <FormButton ghost variant="outlined" className="flex-1">
              {t('common.changePassword')}
            </FormButton>
            <FormButton type="primary" className="flex-1">
              {t('common.navigation.logout')}
            </FormButton>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default Profile;
